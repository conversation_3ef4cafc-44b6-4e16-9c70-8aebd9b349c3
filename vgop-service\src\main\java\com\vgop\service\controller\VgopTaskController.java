package com.vgop.service.controller;

import com.vgop.service.dto.TaskExecutionRequest;
import com.vgop.service.dto.TaskExecutionResponse;
import com.vgop.service.entity.TaskConfig;
import com.vgop.service.service.VgopTaskScheduler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.concurrent.CompletableFuture;

/**
 * VGOP任务管理REST API控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/tasks")
@Api(tags = "VGOP任务管理")
@Validated
public class VgopTaskController {
    
    private final VgopTaskScheduler taskScheduler;
    
    public VgopTaskController(@Qualifier("vgopTaskExecutor") VgopTaskScheduler taskScheduler) {
        this.taskScheduler = taskScheduler;
    }
    
    /**
     * 执行日统计任务
     */
    @PostMapping("/day-stat")
    @ApiOperation(value = "执行日统计任务", notes = "执行VGOP日统计数据导出任务")
    public ResponseEntity<TaskExecutionResponse> executeDayStatTask(
            @ApiParam(value = "任务执行请求", required = true)
            @Valid @RequestBody TaskExecutionRequest request) {
        
        log.info("接收到日统计任务请求: {}", request.getActionInstanceId());
        
        // 设置任务类型
        request.setTaskType(TaskConfig.TaskType.DAY);
        
        try {
            if (request.getAsync() != null && request.getAsync()) {
                // 异步执行
                CompletableFuture<TaskExecutionResponse> future = taskScheduler.executeDayStatTask(request);
                
                // 立即返回任务已提交的响应
                TaskExecutionResponse response = TaskExecutionResponse.builder()
                    .taskId(request.getActionInstanceId())
                    .status(TaskConfig.TaskStatus.PENDING)
                    .success(true)
                    .message("日统计任务已提交，正在异步执行")
                    .build();
                
                return ResponseEntity.ok(response);
                
            } else {
                // 同步执行
                CompletableFuture<TaskExecutionResponse> future = taskScheduler.executeDayStatTask(request);
                TaskExecutionResponse response = future.get();
                
                return ResponseEntity.ok(response);
            }
            
        } catch (Exception e) {
            log.error("日统计任务执行失败", e);
            
            TaskExecutionResponse errorResponse = TaskExecutionResponse.builder()
                .taskId(request.getActionInstanceId())
                .status(TaskConfig.TaskStatus.FAILED)
                .success(false)
                .errorMessage(e.getMessage())
                .message("日统计任务执行失败")
                .build();
            
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }
    
    /**
     * 执行月统计任务
     */
    @PostMapping("/month-stat")
    @ApiOperation(value = "执行月统计任务", notes = "执行VGOP月统计数据导出任务")
    public ResponseEntity<TaskExecutionResponse> executeMonthStatTask(
            @ApiParam(value = "任务执行请求", required = true)
            @Valid @RequestBody TaskExecutionRequest request) {
        
        log.info("接收到月统计任务请求: {}", request.getActionInstanceId());
        
        // 设置任务类型
        request.setTaskType(TaskConfig.TaskType.MONTH);
        
        try {
            if (request.getAsync() != null && request.getAsync()) {
                // 异步执行
                CompletableFuture<TaskExecutionResponse> future = taskScheduler.executeMonthStatTask(request);
                
                // 立即返回任务已提交的响应
                TaskExecutionResponse response = TaskExecutionResponse.builder()
                    .taskId(request.getActionInstanceId())
                    .status(TaskConfig.TaskStatus.PENDING)
                    .success(true)
                    .message("月统计任务已提交，正在异步执行")
                    .build();
                
                return ResponseEntity.ok(response);
                
            } else {
                // 同步执行
                CompletableFuture<TaskExecutionResponse> future = taskScheduler.executeMonthStatTask(request);
                TaskExecutionResponse response = future.get();
                
                return ResponseEntity.ok(response);
            }
            
        } catch (Exception e) {
            log.error("月统计任务执行失败", e);
            
            TaskExecutionResponse errorResponse = TaskExecutionResponse.builder()
                .taskId(request.getActionInstanceId())
                .status(TaskConfig.TaskStatus.FAILED)
                .success(false)
                .errorMessage(e.getMessage())
                .message("月统计任务执行失败")
                .build();
            
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }
    
    /**
     * 查询任务状态
     */
    @GetMapping("/{taskId}/status")
    @ApiOperation(value = "查询任务状态", notes = "根据任务ID查询任务执行状态")
    public ResponseEntity<TaskExecutionResponse> getTaskStatus(
            @ApiParam(value = "任务ID", required = true)
            @PathVariable String taskId) {
        
        log.info("查询任务状态: {}", taskId);
        
        // TODO: 实现任务状态查询逻辑
        // 这里需要维护一个任务状态的存储，可以是内存中的Map或数据库
        
        TaskExecutionResponse response = TaskExecutionResponse.builder()
            .taskId(taskId)
            .status(TaskConfig.TaskStatus.SUCCESS)
            .success(true)
            .message("任务状态查询功能待实现")
            .build();
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 系统健康检查
     */
    @GetMapping("/health")
    @ApiOperation(value = "系统健康检查", notes = "检查VGOP服务健康状态")
    public ResponseEntity<Object> healthCheck() {
        return ResponseEntity.ok().body(new Object() {
            public final String status = "UP";
            public final String service = "VGOP Service";
            public final long timestamp = System.currentTimeMillis();
        });
    }
} 
package com.vgop.service.service;

import com.vgop.service.config.VgopProperties;
import com.vgop.service.entity.FileMetadata;
import com.vgop.service.exception.FileProcessingException;
import com.vgop.service.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件处理服务
 * 实现Shell脚本中的文件分割、格式化和校验文件生成功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileProcessingService {
    
    private final VgopProperties vgopProperties;
    
    /**
     * 处理结果类
     */
    public static class ProcessResult {
        private final long totalLines;
        private final List<String> outputFiles;
        
        public ProcessResult(long totalLines, List<String> outputFiles) {
            this.totalLines = totalLines;
            this.outputFiles = outputFiles;
        }
        
        public long getTotalLines() {
            return totalLines;
        }
        
        public List<String> getOutputFiles() {
            return outputFiles;
        }
    }
    
    /**
     * 处理并分割文件
     * 对应Shell脚本中的200万行分片逻辑
     */
    public ProcessResult splitAndProcessFile(String unloadFilePath, String outputDir, 
                                           String filePrefix, String scriptId, 
                                           String dataDate, String revision) {
        try {
            File unloadFile = new File(unloadFilePath);
            if (!unloadFile.exists()) {
                throw new FileProcessingException("Unload文件不存在: " + unloadFilePath);
            }
            
            // 计算总行数
            long totalLines = countLines(unloadFilePath);
            log.info("开始处理文件: {}, 总行数: {}", unloadFilePath, totalLines);
            
            if (totalLines == 0) {
                totalLines = 1; // Shell脚本中的逻辑：如果为0则设为1
            }
            
            List<FileMetadata> generatedFiles = new ArrayList<>();
            String nowTime = DateTimeUtil.getCurrentTimeString();
            String verfFilePath = generateVerfFilePath(outputDir, filePrefix, dataDate, scriptId, revision);
            
            // 创建校验文件
            try (BufferedWriter verfWriter = Files.newBufferedWriter(Paths.get(verfFilePath), StandardCharsets.UTF_8)) {
                
                int fileNum = 1;
                long processedLines = 0;
                int maxLinesPerFile = vgopProperties.getFileProcessing().getMaxLinesPerFile();
                
                try (BufferedReader reader = Files.newBufferedReader(Paths.get(unloadFilePath), StandardCharsets.UTF_8)) {
                    
                    while (processedLines < totalLines) {
                        String dataFileName = generateDataFileName(filePrefix, dataDate, scriptId, revision, fileNum);
                        String dataFilePath = Paths.get(outputDir, dataFileName).toString();
                        
                        long linesInThisFile = processFileChunk(reader, dataFilePath, 
                                                              Math.min(maxLinesPerFile, (int)(totalLines - processedLines)),
                                                              (int)processedLines + 1);
                        
                        // 生成文件元数据
                        FileMetadata fileMetadata = createFileMetadata(dataFilePath, dataFileName, 
                                                                     linesInThisFile, dataDate, nowTime);
                        generatedFiles.add(fileMetadata);
                        
                        // 写入校验文件记录
                        writeVerfRecord(verfWriter, fileMetadata);
                        
                        processedLines += linesInThisFile;
                        fileNum++;
                        
                        log.debug("生成文件片段: {}, 行数: {}", dataFileName, linesInThisFile);
                    }
                }
            }
            
            log.info("文件处理完成: 生成 {} 个文件片段", generatedFiles.size());
            
            // 将FileMetadata列表转换为文件路径列表
            List<String> outputFilePaths = generatedFiles.stream()
                .map(FileMetadata::getFilePath)
                .collect(Collectors.toList());
            
            return new ProcessResult(totalLines, outputFilePaths);
            
        } catch (Exception e) {
            log.error("文件处理失败: {}", unloadFilePath, e);
            throw new FileProcessingException("文件处理失败", e);
        }
    }
    
    /**
     * 处理并分割文件（重载方法，接受verfFileName参数）
     */
    public ProcessResult splitAndProcessFile(String unloadFilePath, String outputDir, 
                                           String filePrefix, String verfFileName, 
                                           String dataDate) {
        try {
            File unloadFile = new File(unloadFilePath);
            if (!unloadFile.exists()) {
                throw new FileProcessingException("Unload文件不存在: " + unloadFilePath);
            }
            
            // 计算总行数
            long totalLines = countLines(unloadFilePath);
            log.info("开始处理文件: {}, 总行数: {}", unloadFilePath, totalLines);
            
            if (totalLines == 0) {
                totalLines = 1; // Shell脚本中的逻辑：如果为0则设为1
            }
            
            List<FileMetadata> generatedFiles = new ArrayList<>();
            String nowTime = DateTimeUtil.getCurrentTimeString();
            String verfFilePath = Paths.get(outputDir, verfFileName).toString();
            
            // 创建校验文件
            try (BufferedWriter verfWriter = Files.newBufferedWriter(Paths.get(verfFilePath), StandardCharsets.UTF_8)) {
                
                int fileNum = 1;
                long processedLines = 0;
                int maxLinesPerFile = vgopProperties.getFileProcessing().getMaxLinesPerFile();
                
                try (BufferedReader reader = Files.newBufferedReader(Paths.get(unloadFilePath), StandardCharsets.UTF_8)) {
                    
                    while (processedLines < totalLines) {
                        // 解析文件名中的信息
                        String[] parts = verfFileName.split("_");
                        String scriptId = parts.length >= 3 ? parts[2].split("\\.")[0] : "";
                        String revision = "00"; // 默认版本号
                        
                        String dataFileName = generateDataFileName(filePrefix, dataDate, scriptId, revision, fileNum);
                        String dataFilePath = Paths.get(outputDir, dataFileName).toString();
                        
                        long linesInThisFile = processFileChunk(reader, dataFilePath, 
                                                              Math.min(maxLinesPerFile, (int)(totalLines - processedLines)),
                                                              (int)processedLines + 1);
                        
                        // 生成文件元数据
                        FileMetadata fileMetadata = createFileMetadata(dataFilePath, dataFileName, 
                                                                     linesInThisFile, dataDate, nowTime);
                        generatedFiles.add(fileMetadata);
                        
                        // 写入校验文件记录
                        writeVerfRecord(verfWriter, fileMetadata);
                        
                        processedLines += linesInThisFile;
                        fileNum++;
                        
                        log.debug("生成文件片段: {}, 行数: {}", dataFileName, linesInThisFile);
                    }
                }
            }
            
            log.info("文件处理完成: 生成 {} 个文件片段", generatedFiles.size());
            
            // 将FileMetadata列表转换为文件路径列表
            List<String> outputFilePaths = generatedFiles.stream()
                .map(FileMetadata::getFilePath)
                .collect(Collectors.toList());
            
            return new ProcessResult(totalLines, outputFilePaths);
            
        } catch (Exception e) {
            log.error("文件处理失败: {}", unloadFilePath, e);
            throw new FileProcessingException("文件处理失败", e);
        }
    }
    
    /**
     * 处理文件块
     * 对应Shell脚本中的格式化逻辑：添加行号、替换分隔符、添加行结束符
     */
    private long processFileChunk(BufferedReader reader, String outputFilePath, 
                                int maxLines, int startLineNum) throws IOException {
        
        long actualLines = 0;
        String lineDelimiterReplacement = vgopProperties.getFileProcessing().getLineDelimiterReplacement();
        String lineEnding = vgopProperties.getFileProcessing().getLineEnding();
        String originalSeparator = vgopProperties.getDatabase().getDefaultColumnSeparator();
        
        try (BufferedWriter writer = Files.newBufferedWriter(Paths.get(outputFilePath), StandardCharsets.UTF_8)) {
            
            String line;
            int currentLineNum = startLineNum;
            
            while ((line = reader.readLine()) != null && actualLines < maxLines) {
                // 对应Shell脚本: nl -s '|' | sed 's/|$//' | tr -d '\' | tr -d ' ' | tr '|' '\200' | awk '{print($0"\r")}'
                
                // 1. 添加行号 (nl -s '|')
                StringBuilder processedLine = new StringBuilder();
                processedLine.append(currentLineNum).append(originalSeparator);
                
                // 2. 添加原始数据
                processedLine.append(line);
                
                // 3. 移除行尾的分隔符 (sed 's/|$//')
                if (processedLine.toString().endsWith(originalSeparator)) {
                    processedLine.setLength(processedLine.length() - originalSeparator.length());
                }
                
                // 4. 移除反斜杠和空格 (tr -d '\' | tr -d ' ')
                String cleaned = processedLine.toString()
                    .replace("\\", "")
                    .replace(" ", "");
                
                // 5. 替换分隔符 (tr '|' '\200')
                String finalLine = cleaned.replace(originalSeparator, lineDelimiterReplacement);
                
                // 6. 添加行结束符 (awk '{print($0"\r")}')
                finalLine += lineEnding;
                
                writer.write(finalLine);
                writer.newLine();
                
                actualLines++;
                currentLineNum++;
            }
        }
        
        return actualLines;
    }
    
    /**
     * 计算文件行数
     */
    private long countLines(String filePath) throws IOException {
        try (BufferedReader reader = Files.newBufferedReader(Paths.get(filePath))) {
            return reader.lines().count();
        }
    }
    
    /**
     * 生成数据文件名
     * 格式: {prefix}_{date}_{scriptId}_{revision}_{fileNum}.dat
     */
    private String generateDataFileName(String prefix, String dataDate, String scriptId, 
                                      String revision, int fileNum) {
        String fileNumStr = String.format("%03d", fileNum);
        return String.format("%s_%s_%s_%s_%s%s", 
                           prefix, dataDate, scriptId, revision, fileNumStr,
                           vgopProperties.getFileProcessing().getDataFileSuffix());
    }
    
    /**
     * 生成校验文件路径
     */
    private String generateVerfFilePath(String outputDir, String prefix, String dataDate, 
                                      String scriptId, String revision) {
        String verfFileName = String.format("%s_%s_%s_%s%s", 
                                           prefix, dataDate, scriptId, revision,
                                           vgopProperties.getFileProcessing().getVerfFileSuffix());
        return Paths.get(outputDir, verfFileName).toString();
    }
    
    /**
     * 创建文件元数据
     */
    private FileMetadata createFileMetadata(String filePath, String fileName, 
                                          long lineCount, String dataDate, String generateTime) throws IOException {
        File file = new File(filePath);
        long fileSize = file.length();
        
        return FileMetadata.builder()
            .fileName(fileName)
            .filePath(filePath)
            .fileSize(fileSize)
            .lineCount(lineCount)
            .dataDate(dataDate)
            .generateTime(generateTime)
            .status(FileMetadata.FileStatus.COMPLETED)
            .build();
    }
    
    /**
     * 写入校验文件记录
     * 对应Shell脚本: echo "$(basename ${FileName}) $(wc -c ${FileName}) $(wc -l ${FileName}) ${BeforeDay} ${nowdate}"
     */
    private void writeVerfRecord(BufferedWriter writer, FileMetadata fileMetadata) throws IOException {
        String lineDelimiterReplacement = vgopProperties.getFileProcessing().getLineDelimiterReplacement();
        String lineEnding = vgopProperties.getFileProcessing().getLineEnding();
        
        // 格式: 文件名|文件大小|行数|日期|生成时间
        String record = String.format("%s|%d|%d|%s|%s",
                                     fileMetadata.getFileName(),
                                     fileMetadata.getFileSize(),
                                     fileMetadata.getLineCount(),
                                     fileMetadata.getDataDate(),
                                     fileMetadata.getGenerateTime());
        
        // 替换分隔符并添加行结束符
        String processedRecord = record.replace("|", lineDelimiterReplacement) + lineEnding;
        
        writer.write(processedRecord);
        writer.newLine();
    }
    
    /**
     * 确保输出目录存在
     */
    public void ensureDirectoryExists(String directoryPath) {
        try {
            Path path = Paths.get(directoryPath);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
                log.debug("创建目录: {}", directoryPath);
            }
        } catch (IOException e) {
            throw new FileProcessingException("创建目录失败: " + directoryPath, e);
        }
    }
    
    /**
     * 生成输出目录路径
     */
    public String generateOutputDirectory(String imagePath, String dataDate, String subPath) {
        return String.format("%s%s/%s/%s/", 
                           imagePath, 
                           vgopProperties.getFileProcessing().getDataRootPath(),
                           dataDate, 
                           subPath);
    }
} 
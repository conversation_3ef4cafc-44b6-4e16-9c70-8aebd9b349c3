package com.vgop.service.service;

import com.vgop.service.config.VgopProperties;
import com.vgop.service.dto.TaskExecutionRequest;
import com.vgop.service.dto.TaskExecutionResponse;
import com.vgop.service.entity.TaskConfig;
import com.vgop.service.exception.TaskExecutionException;
import com.vgop.service.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * VGOP任务调度服务
 */
@Slf4j
@Service("vgopTaskExecutor")
@RequiredArgsConstructor
public class VgopTaskScheduler {
    
    private final DataExportService dataExportService;
    private final FileProcessingService fileProcessingService;
    private final RevisionService revisionService;
    private final VgopProperties vgopProperties;
    
    /**
     * 执行日统计任务
     */
    @Async("taskExecutor")
    public CompletableFuture<TaskExecutionResponse> executeDayStatTask(TaskExecutionRequest request) {
        log.info("开始执行日统计任务: {}", request.getActionInstanceId());
        
        LocalDateTime startTime = LocalDateTime.now();
        TaskExecutionResponse response = TaskExecutionResponse.builder()
            .taskId(request.getActionInstanceId())
            .status(TaskConfig.TaskStatus.RUNNING)
            .startTime(startTime)
            .success(false)
            .build();
        
        try {
            List<String> generatedFiles = new ArrayList<>();
            
            // 执行日统计各个脚本
            String result1 = executeBusinessAnalysis(request);
            if (result1 != null) generatedFiles.add(result1);
            
            String result2 = executeDailyNewMajorUsers(request);
            if (result2 != null) generatedFiles.add(result2);
            
            String result3 = executeMinorUsers(request);
            if (result3 != null) generatedFiles.add(result3);
            
            String result4 = executeDailyUserActivityLogs(request);
            if (result4 != null) generatedFiles.add(result4);
            
            String result5 = executeDailyCallRecords(request);
            if (result5 != null) generatedFiles.add(result5);
            
            String result6 = executeDailySmsLogs(request);
            if (result6 != null) generatedFiles.add(result6);
            
            String result7 = executeDailyNewSecUsers(request);
            if (result7 != null) generatedFiles.add(result7);
            
            String result8 = executeServiceTypes(request);
            if (result8 != null) generatedFiles.add(result8);
            
            String result9 = executeChannelInfo(request);
            if (result9 != null) generatedFiles.add(result9);
            
            String result10 = executeShutdownReasons(request);
            if (result10 != null) generatedFiles.add(result10);
            
            String result11 = executeMcnTypes(request);
            if (result11 != null) generatedFiles.add(result11);
            
            LocalDateTime endTime = LocalDateTime.now();
            long executionTime = java.time.Duration.between(startTime, endTime).toMillis();
            
            response.setStatus(TaskConfig.TaskStatus.SUCCESS);
            response.setEndTime(endTime);
            response.setExecutionTimeMs(executionTime);
            response.setGeneratedFiles(generatedFiles);
            response.setSuccess(true);
            response.setMessage("日统计任务执行成功");
            
            log.info("日统计任务执行完成: {}", request.getActionInstanceId());
            
        } catch (Exception e) {
            LocalDateTime endTime = LocalDateTime.now();
            long executionTime = java.time.Duration.between(startTime, endTime).toMillis();
            
            response.setStatus(TaskConfig.TaskStatus.FAILED);
            response.setEndTime(endTime);
            response.setExecutionTimeMs(executionTime);
            response.setErrorMessage(e.getMessage());
            response.setMessage("日统计任务执行失败");
            
            log.error("日统计任务执行失败: {}", request.getActionInstanceId(), e);
        }
        
        return CompletableFuture.completedFuture(response);
    }
    
    /**
     * 执行月统计任务
     */
    @Async("taskExecutor")
    public CompletableFuture<TaskExecutionResponse> executeMonthStatTask(TaskExecutionRequest request) {
        log.info("开始执行月统计任务: {}", request.getActionInstanceId());
        
        LocalDateTime startTime = LocalDateTime.now();
        TaskExecutionResponse response = TaskExecutionResponse.builder()
            .taskId(request.getActionInstanceId())
            .status(TaskConfig.TaskStatus.RUNNING)
            .startTime(startTime)
            .success(false)
            .build();
        
        try {
            List<String> generatedFiles = new ArrayList<>();
            
            // 执行月统计各个脚本
            String result1 = executeMajorUsersSnapshot(request);
            if (result1 != null) generatedFiles.add(result1);
            
            String result2 = executeMinorUsersSnapshot(request);
            if (result2 != null) generatedFiles.add(result2);
            
            String result3 = executeMonthlyOpLogs(request);
            if (result3 != null) generatedFiles.add(result3);
            
            String result4 = executeSecUsersSnapshot(request);
            if (result4 != null) generatedFiles.add(result4);
            
            LocalDateTime endTime = LocalDateTime.now();
            long executionTime = java.time.Duration.between(startTime, endTime).toMillis();
            
            response.setStatus(TaskConfig.TaskStatus.SUCCESS);
            response.setEndTime(endTime);
            response.setExecutionTimeMs(executionTime);
            response.setGeneratedFiles(generatedFiles);
            response.setSuccess(true);
            response.setMessage("月统计任务执行成功");
            
            log.info("月统计任务执行完成: {}", request.getActionInstanceId());
            
        } catch (Exception e) {
            LocalDateTime endTime = LocalDateTime.now();
            long executionTime = java.time.Duration.between(startTime, endTime).toMillis();
            
            response.setStatus(TaskConfig.TaskStatus.FAILED);
            response.setEndTime(endTime);
            response.setExecutionTimeMs(executionTime);
            response.setErrorMessage(e.getMessage());
            response.setMessage("月统计任务执行失败");
            
            log.error("月统计任务执行失败: {}", request.getActionInstanceId(), e);
        }
        
        return CompletableFuture.completedFuture(response);
    }
    
    /**
     * 执行业务分析统计
     */
    private String executeBusinessAnalysis(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportBanalyseData(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId(),
                request.getTraceFlag(),
                request.getSourceDbName()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.11-24101", "i_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("业务分析统计执行失败", e);
        }
    }
    
    /**
     * 执行主号用户全量快照
     * 对应VGOP1-R2.10-24201month.sh
     */
    private String executeMajorUsersSnapshot(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportMajorUsersSnapshot(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.10-24201month", "i_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("主号用户全量快照执行失败", e);
        }
    }
    
    /**
     * 执行副号用户全量快照
     * 对应VGOP1-R2.10-24202month.sh
     */
    private String executeMinorUsersSnapshot(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportMinorUsersSnapshot(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.10-24202month", "i_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("副号用户全量快照执行失败", e);
        }
    }
    
    /**
     * 执行月度操作日志
     * 对应VGOP1-R2.10-24204.sh
     */
    private String executeMonthlyOpLogs(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportMonthlyOpLogs(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.10-24204", "i_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("月度操作日志执行失败", e);
        }
    }
    
    /**
     * 执行实体副号用户全量快照
     * 对应VGOP1-R2.10-24207month.sh
     */
    private String executeSecUsersSnapshot(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportSecUsersSnapshot(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.10-24207month", "i_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("实体副号用户全量快照执行失败", e);
        }
    }
    
    /**
     * 执行每日新增主号用户
     * 对应VGOP1-R2.10-24201day.sh
     */
    private String executeDailyNewMajorUsers(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportDailyNewMajorUsers(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.10-24201", "a_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("每日新增主号用户执行失败", e);
        }
    }
    
    /**
     * 执行副号用户信息
     * 对应VGOP1-R2.10-24202day.sh
     */
    private String executeMinorUsers(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportMinorUsers(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.10-24202", "i_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("副号用户信息执行失败", e);
        }
    }
    
    /**
     * 执行每日用户活动日志
     * 对应VGOP1-R2.10-24203.sh
     */
    private String executeDailyUserActivityLogs(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportDailyUserActivityLogs(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.10-24203", "a_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("每日用户活动日志执行失败", e);
        }
    }
    
    /**
     * 执行每日通话话单记录
     * 对应VGOP1-R2.10-24205.sh
     */
    private String executeDailyCallRecords(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportDailyCallRecords(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.10-24205", "a_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("每日通话话单记录执行失败", e);
        }
    }
    
    /**
     * 执行每日短信日志
     * 对应VGOP1-R2.10-24206.sh
     */
    private String executeDailySmsLogs(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportDailySmsLogs(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.10-24206", "a_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("每日短信日志执行失败", e);
        }
    }
    
    /**
     * 执行每日新增实体副号用户
     * 对应VGOP1-R2.10-24207day.sh
     */
    private String executeDailyNewSecUsers(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportDailyNewSecUsers(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.10-24207", "a_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("每日新增实体副号用户执行失败", e);
        }
    }
    
    /**
     * 执行服务类型维表
     * 对应VGOP1-R2.13-24301.sh
     */
    private String executeServiceTypes(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportServiceTypes(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.13-24301", "i_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("服务类型维表执行失败", e);
        }
    }
    
    /**
     * 执行渠道信息维表
     * 对应VGOP1-R2.13-24302.sh
     */
    private String executeChannelInfo(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportChannelInfo(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.13-24302", "i_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("渠道信息维表执行失败", e);
        }
    }
    
    /**
     * 执行关机原因维表
     * 对应VGOP1-R2.13-24303.sh
     */
    private String executeShutdownReasons(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportShutdownReasons(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.13-24303", "i_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("关机原因维表执行失败", e);
        }
    }
    
    /**
     * 执行MCN类型维表
     * 对应VGOP1-R2.13-24304.sh
     */
    private String executeMcnTypes(TaskExecutionRequest request) {
        try {
            String unloadFilePath = dataExportService.exportMcnTypes(
                request.getActionInstanceId(),
                request.getImagePath(),
                request.getDateId()
            );
            
            return processFile(unloadFilePath, request, "VGOP1-R2.13-24304", "i_10000");
            
        } catch (Exception e) {
            throw new TaskExecutionException("MCN类型维表执行失败", e);
        }
    }

    /**
     * 处理文件
     */
    private String processFile(String unloadFilePath, TaskExecutionRequest request, 
                              String scriptId, String filePrefix) {
        if (unloadFilePath == null) {
            return null;
        }
        
        String beforeDay = DateTimeUtil.calculateBeforeDay(request.getDateId());
        String outputDir = fileProcessingService.generateOutputDirectory(
            request.getImagePath(), beforeDay, "day");
        fileProcessingService.ensureDirectoryExists(outputDir);
        
        String tmpFileName = String.format("%s_%s_%s.unl", filePrefix, beforeDay, scriptId);
        String revision = revisionService.getNextRevision(beforeDay, tmpFileName);
        
        fileProcessingService.splitAndProcessFile(unloadFilePath, outputDir, 
                filePrefix, scriptId, beforeDay, revision);
        
        return outputDir;
    }
} 